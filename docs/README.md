# HaoWriter - 小说分析网站文档

## 项目概述

HaoWriter 是一个专业的小说分析与拆解管理平台，旨在帮助用户系统化地记录、管理和分析小说内容，提供结构化的分析拆解工具，支持深度阅读和创作学习。

## 文档目录

### 📋 [需求文档](./requirements.md)
详细的功能需求说明，包括：
- 项目目标和用户群体
- 分类管理系统
- 小说管理系统  
- 分析记录系统
- 用户界面设计要求
- 技术架构建议
- 开发优先级和里程碑规划

### 🔌 [API设计规范](./api-design.md)
完整的后端API接口设计，包括：
- RESTful API设计原则
- 统一的请求/响应格式
- 分类、小说、分析记录的CRUD接口
- 搜索和统计接口
- 错误码定义和安全考虑

### 🗄️ [数据库设计](./database-design.md)
详细的数据库结构设计，包括：
- 数据库选择建议（MySQL/PostgreSQL）
- 完整的表结构设计
- 索引策略和性能优化
- 数据完整性约束
- 触发器和存储过程

### 🎨 [前端技术规范](./frontend-specification.md)
前端开发的技术标准，包括：
- Vue 3 + TypeScript技术栈
- 项目结构和组件设计规范
- 状态管理和路由配置
- 样式规范和UI组件设计
- 性能优化策略

## 核心功能特性

### 🏷️ 分类管理
- 创建、编辑、删除小说分类
- 支持分类排序和层级管理
- 分类统计信息展示

### 📚 小说管理
- 小说基本信息管理（标题、作者、简介等）
- 支持封面图片和标签系统
- 阅读状态和个人评分
- 小说搜索和筛选功能

### 📝 分析记录
- **原文部分**：摘录小说原文内容
- **评论部分**：深度分析和拆解评论
- 支持Markdown格式富文本编辑
- 分析类型分类（情节、人物、技巧等）
- 重要程度标记和标签系统

### 🔍 搜索功能
- 全文搜索支持
- 按分类、作者、标签筛选
- 高级搜索和组合条件

### 📊 统计分析
- 阅读统计和分析记录统计
- 个人阅读习惯分析
- 数据可视化展示

## 技术架构

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI库**: Element Plus
- **状态管理**: Pinia
- **构建工具**: Vite
- **Markdown编辑器**: 集成专业编辑器组件

### 后端技术栈
- **框架**: Node.js + Express/Nest.js
- **数据库**: MySQL 8.0+ / PostgreSQL
- **ORM**: TypeORM / Prisma
- **认证**: JWT
- **API文档**: Swagger/OpenAPI

### 部署方案
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **数据库**: 容器化部署
- **文件存储**: 本地存储或云存储

## 开发计划

### 第一阶段：核心功能（4-6周）
- [x] 需求分析和技术选型
- [ ] 基础架构搭建
- [ ] 分类和小说管理功能
- [ ] 分析记录核心功能

### 第二阶段：功能完善（3-4周）
- [ ] 用户体验优化
- [ ] 高级搜索功能
- [ ] 数据导出功能
- [ ] 响应式设计完善

### 第三阶段：测试部署（2-3周）
- [ ] 单元测试和集成测试
- [ ] 性能优化
- [ ] 生产环境部署
- [ ] 用户文档编写

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+ 或 PostgreSQL 14+
- Docker（可选）

### 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd HaoWriter

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 启动数据库（Docker方式）
docker-compose up -d mysql

# 运行数据库迁移
npm run migrate

# 启动开发服务器
npm run dev
```

### 项目结构
```
HaoWriter/
├── docs/                   # 项目文档
├── frontend/              # 前端代码
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/               # 后端代码
│   ├── src/
│   ├── migrations/
│   └── package.json
├── docker-compose.yml     # Docker配置
└── README.md
```

## 贡献指南

### 开发规范
1. 遵循项目的代码规范和命名约定
2. 提交前运行测试和代码检查
3. 编写清晰的提交信息
4. 更新相关文档

### 提交流程
1. Fork项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

## 联系方式

- 项目维护者：[您的姓名]
- 邮箱：[您的邮箱]
- 项目地址：[GitHub仓库地址]

## 更新日志

### v1.0.0 (计划中)
- 基础功能实现
- 分类、小说、分析记录管理
- Markdown编辑器集成
- 搜索和筛选功能

---

*本文档会随着项目开发进度持续更新，请关注最新版本。*
