# API 设计规范

## API 设计原则

### RESTful 设计
- 使用标准HTTP方法（GET、POST、PUT、DELETE）
- 资源导向的URL设计
- 统一的响应格式
- 合理的HTTP状态码使用

### 响应格式标准
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": [
      {
        "field": "title",
        "message": "标题不能为空"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## API 端点设计

### 分类管理 API

#### 获取分类列表
```
GET /api/categories
Query参数：
- sort: string (name|created_at|sort_order) 排序字段
- order: string (asc|desc) 排序方向

响应：
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "玄幻小说",
      "description": "包含修仙、魔法等元素的小说",
      "sort_order": 1,
      "novels_count": 5,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 创建分类
```
POST /api/categories
请求体：
{
  "name": "科幻小说",
  "description": "科幻题材小说分类",
  "sort_order": 2
}
```

#### 更新分类
```
PUT /api/categories/:id
请求体：
{
  "name": "科幻小说",
  "description": "更新后的描述",
  "sort_order": 3
}
```

#### 删除分类
```
DELETE /api/categories/:id
Query参数：
- force: boolean 是否强制删除（包含小说时）
```

### 小说管理 API

#### 获取小说列表
```
GET /api/novels
Query参数：
- category_id: number 分类ID筛选
- search: string 搜索关键词
- status: string 阅读状态筛选
- page: number 页码
- limit: number 每页数量
- sort: string 排序字段
- order: string 排序方向

响应：
{
  "success": true,
  "data": {
    "novels": [
      {
        "id": 1,
        "category_id": 1,
        "title": "斗破苍穹",
        "author": "天蚕土豆",
        "description": "这里是一个属于斗气的世界...",
        "cover_image": "/uploads/covers/1.jpg",
        "tags": ["玄幻", "修炼", "热血"],
        "reading_status": "reading",
        "personal_rating": 4,
        "records_count": 12,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "category": {
          "id": 1,
          "name": "玄幻小说"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

#### 获取小说详情
```
GET /api/novels/:id
响应包含小说完整信息和最近的分析记录
```

#### 创建小说
```
POST /api/novels
请求体：
{
  "category_id": 1,
  "title": "完美世界",
  "author": "辰东",
  "description": "一粒尘可填海，一根草斩尽日月星辰...",
  "cover_image": "/uploads/covers/2.jpg",
  "tags": ["玄幻", "修炼"],
  "reading_status": "not_started",
  "personal_rating": null
}
```

#### 更新小说
```
PUT /api/novels/:id
请求体格式同创建小说
```

#### 删除小说
```
DELETE /api/novels/:id
Query参数：
- force: boolean 是否强制删除（包含分析记录时）
```

### 分析记录 API

#### 获取分析记录列表
```
GET /api/novels/:novel_id/records
Query参数：
- search: string 搜索关键词
- analysis_type: string 分析类型筛选
- importance_level: number 重要程度筛选
- page: number 页码
- limit: number 每页数量
- sort: string 排序字段
- order: string 排序方向

响应：
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "novel_id": 1,
        "title": "主角初次修炼场景分析",
        "chapter_info": "第一章",
        "original_text": "萧炎缓缓睁开眼眸...",
        "comment_text": "这段描写很好地展现了...",
        "analysis_type": "情节分析",
        "importance_level": 4,
        "tags": ["修炼", "成长"],
        "page_location": "第15页",
        "word_count": 256,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 50,
      "total_pages": 5
    }
  }
}
```

#### 获取分析记录详情
```
GET /api/records/:id
```

#### 创建分析记录
```
POST /api/novels/:novel_id/records
请求体：
{
  "title": "人物性格分析",
  "chapter_info": "第三章",
  "original_text": "萧炎眼中闪过一丝坚毅...",
  "comment_text": "通过这个细节可以看出主角的坚韧性格...",
  "analysis_type": "人物分析",
  "importance_level": 3,
  "tags": ["性格", "成长"],
  "page_location": "第45页"
}
```

#### 更新分析记录
```
PUT /api/records/:id
请求体格式同创建记录
```

#### 删除分析记录
```
DELETE /api/records/:id
```

#### 批量删除分析记录
```
DELETE /api/records/batch
请求体：
{
  "record_ids": [1, 2, 3, 4]
}
```

### 搜索 API

#### 全局搜索
```
GET /api/search
Query参数：
- q: string 搜索关键词
- type: string (novels|records|all) 搜索类型
- category_id: number 分类筛选
- page: number 页码
- limit: number 每页数量

响应：
{
  "success": true,
  "data": {
    "novels": [...],
    "records": [...],
    "total_count": 25,
    "pagination": {...}
  }
}
```

### 统计 API

#### 获取统计信息
```
GET /api/statistics
响应：
{
  "success": true,
  "data": {
    "categories_count": 5,
    "novels_count": 23,
    "records_count": 156,
    "total_words": 45678,
    "recent_activity": [
      {
        "type": "record_created",
        "title": "新增分析记录",
        "novel_title": "斗破苍穹",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 导出 API

#### 导出小说分析记录
```
GET /api/novels/:id/export
Query参数：
- format: string (markdown|pdf|json) 导出格式
- include_original: boolean 是否包含原文
- include_comments: boolean 是否包含评论

响应：文件下载或导出链接
```

## 错误码定义

### 通用错误码
- `INVALID_REQUEST`: 请求格式错误
- `VALIDATION_ERROR`: 参数验证失败
- `NOT_FOUND`: 资源不存在
- `DUPLICATE_ERROR`: 资源重复
- `PERMISSION_DENIED`: 权限不足
- `INTERNAL_ERROR`: 服务器内部错误

### 业务错误码
- `CATEGORY_NOT_EMPTY`: 分类下还有小说，无法删除
- `NOVEL_NOT_EMPTY`: 小说下还有分析记录，无法删除
- `INVALID_CATEGORY`: 无效的分类ID
- `INVALID_NOVEL`: 无效的小说ID
- `DUPLICATE_NOVEL_TITLE`: 同分类下小说标题重复

## 请求限制

### 频率限制
- 普通API：每分钟100次请求
- 搜索API：每分钟30次请求
- 导出API：每分钟5次请求

### 数据限制
- 分类名称：最长100字符
- 小说标题：最长200字符
- 原文内容：最长50000字符
- 评论内容：最长50000字符
- 标签数量：最多10个
- 单次批量操作：最多100条记录

## 安全考虑

### 输入验证
- 所有用户输入必须进行验证和清理
- 防止SQL注入和XSS攻击
- 文件上传类型和大小限制

### 数据保护
- 敏感数据加密存储
- API访问日志记录
- 定期数据备份

### 访问控制
- JWT token认证（如需要）
- API访问频率限制
- 危险操作二次确认

---

*此API设计文档将随着开发进度持续更新和完善。*
