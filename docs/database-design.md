# 数据库设计详细说明

## 数据库选择

### 推荐方案：MySQL 8.0+
- **优势**：成熟稳定、性能优秀、社区支持好
- **JSON支持**：原生支持JSON数据类型，适合存储标签等数据
- **全文搜索**：支持中文全文索引
- **事务支持**：ACID特性保证数据一致性

### 备选方案：PostgreSQL 14+
- **优势**：功能更丰富、扩展性更好
- **JSON支持**：更强大的JSON操作功能
- **全文搜索**：更好的全文搜索支持

## 详细表结构设计

### 1. categories 表（分类表）

```sql
CREATE TABLE categories (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序权重，数值越小越靠前',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记：0-正常，1-已删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_name (name, is_deleted) COMMENT '分类名称唯一约束（排除已删除）',
    KEY idx_sort_order (sort_order) COMMENT '排序索引',
    KEY idx_created_at (created_at) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小说分类表';
```

#### 字段说明
- `id`: 主键，自增长
- `name`: 分类名称，必填且唯一
- `description`: 分类描述，可选
- `sort_order`: 排序权重，用于自定义排序
- `is_deleted`: 软删除标记，支持数据恢复
- `created_at/updated_at`: 时间戳字段

#### 业务规则
- 分类名称在未删除状态下必须唯一
- 删除分类时检查是否有关联小说
- 支持按排序权重自定义顺序

### 2. novels 表（小说表）

```sql
CREATE TABLE novels (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '小说ID',
    category_id BIGINT UNSIGNED NOT NULL COMMENT '分类ID',
    title VARCHAR(200) NOT NULL COMMENT '小说标题',
    author VARCHAR(100) NOT NULL COMMENT '作者姓名',
    description TEXT COMMENT '小说简介，支持Markdown格式',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    tags JSON COMMENT '标签数组，如["玄幻","修炼","热血"]',
    reading_status ENUM('not_started', 'reading', 'completed', 'paused') NOT NULL DEFAULT 'not_started' COMMENT '阅读状态',
    personal_rating TINYINT UNSIGNED COMMENT '个人评分，1-5星',
    word_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总字数统计',
    records_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '分析记录数量',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记：0-正常，1-已删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_category_title (category_id, title, is_deleted) COMMENT '同分类下标题唯一约束',
    KEY idx_category_id (category_id) COMMENT '分类索引',
    KEY idx_author (author) COMMENT '作者索引',
    KEY idx_reading_status (reading_status) COMMENT '阅读状态索引',
    KEY idx_personal_rating (personal_rating) COMMENT '评分索引',
    KEY idx_created_at (created_at) COMMENT '创建时间索引',
    FULLTEXT KEY ft_title_author (title, author) COMMENT '标题作者全文索引',
    
    CONSTRAINT fk_novels_category_id FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小说信息表';
```

#### 字段说明
- `category_id`: 外键关联分类表
- `title`: 小说标题，同分类下唯一
- `author`: 作者姓名
- `description`: 简介，支持Markdown格式
- `cover_image`: 封面图片URL
- `tags`: JSON格式存储标签数组
- `reading_status`: 阅读状态枚举
- `personal_rating`: 1-5星评分
- `word_count`: 总字数，通过触发器或应用层维护
- `records_count`: 分析记录数量，通过触发器或应用层维护

#### 业务规则
- 同分类下小说标题不能重复
- 删除时检查是否有分析记录
- 评分范围1-5星，可为空
- 外键约束防止删除有小说的分类

### 3. analysis_records 表（分析记录表）

```sql
CREATE TABLE analysis_records (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    novel_id BIGINT UNSIGNED NOT NULL COMMENT '小说ID',
    title VARCHAR(200) COMMENT '记录标题',
    chapter_info VARCHAR(100) COMMENT '章节信息，如"第一章"',
    original_text LONGTEXT COMMENT '原文内容，支持Markdown格式',
    comment_text LONGTEXT COMMENT '评论分析内容，支持Markdown格式',
    analysis_type VARCHAR(50) COMMENT '分析类型：情节分析、人物分析、写作技巧等',
    importance_level TINYINT UNSIGNED NOT NULL DEFAULT 3 COMMENT '重要程度，1-5级',
    tags JSON COMMENT '标签数组',
    page_location VARCHAR(100) COMMENT '页码或位置信息',
    word_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '字数统计（原文+评论）',
    view_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '查看次数',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记：0-正常，1-已删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (id),
    KEY idx_novel_id (novel_id) COMMENT '小说ID索引',
    KEY idx_analysis_type (analysis_type) COMMENT '分析类型索引',
    KEY idx_importance_level (importance_level) COMMENT '重要程度索引',
    KEY idx_created_at (created_at) COMMENT '创建时间索引',
    FULLTEXT KEY ft_content (original_text, comment_text) COMMENT '内容全文索引',
    
    CONSTRAINT fk_records_novel_id FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT chk_importance_level CHECK (importance_level BETWEEN 1 AND 5),
    CONSTRAINT chk_content_not_empty CHECK (
        (original_text IS NOT NULL AND CHAR_LENGTH(original_text) > 0) OR 
        (comment_text IS NOT NULL AND CHAR_LENGTH(comment_text) > 0)
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析记录表';
```

#### 字段说明
- `novel_id`: 外键关联小说表
- `title`: 记录标题，可选
- `chapter_info`: 章节信息
- `original_text`: 原文内容，支持Markdown
- `comment_text`: 评论分析内容，支持Markdown
- `analysis_type`: 分析类型分类
- `importance_level`: 重要程度1-5级
- `tags`: JSON格式标签
- `page_location`: 页码位置信息
- `word_count`: 字数统计
- `view_count`: 查看次数统计

#### 业务规则
- 原文和评论至少有一个不能为空
- 重要程度必须在1-5之间
- 删除小说时级联删除分析记录
- 支持全文搜索

### 4. 辅助表设计

#### tags 表（标签表）- 可选
```sql
CREATE TABLE tags (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    name VARCHAR(50) NOT NULL COMMENT '标签名称',
    type ENUM('novel', 'record', 'common') NOT NULL DEFAULT 'common' COMMENT '标签类型',
    usage_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_name_type (name, type) COMMENT '同类型标签名称唯一',
    KEY idx_usage_count (usage_count) COMMENT '使用次数索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';
```

## 索引策略

### 主要索引
1. **主键索引**：所有表的id字段
2. **唯一索引**：分类名称、小说标题（分类内）
3. **外键索引**：category_id、novel_id
4. **业务索引**：排序字段、状态字段、时间字段
5. **全文索引**：标题、作者、内容字段

### 复合索引
```sql
-- 小说查询优化
ALTER TABLE novels ADD INDEX idx_category_status_rating (category_id, reading_status, personal_rating);

-- 分析记录查询优化
ALTER TABLE analysis_records ADD INDEX idx_novel_type_importance (novel_id, analysis_type, importance_level);

-- 时间范围查询优化
ALTER TABLE analysis_records ADD INDEX idx_novel_created (novel_id, created_at);
```

## 数据完整性约束

### 外键约束
- novels.category_id → categories.id
- analysis_records.novel_id → novels.id

### 检查约束
- personal_rating: 1-5范围检查
- importance_level: 1-5范围检查
- 内容非空检查：原文或评论至少一个不为空

### 触发器设计

#### 更新统计数据触发器
```sql
-- 更新小说记录数量
DELIMITER $$
CREATE TRIGGER tr_update_novel_records_count
AFTER INSERT ON analysis_records
FOR EACH ROW
BEGIN
    UPDATE novels 
    SET records_count = records_count + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.novel_id;
END$$

CREATE TRIGGER tr_update_novel_records_count_delete
AFTER DELETE ON analysis_records
FOR EACH ROW
BEGIN
    UPDATE novels 
    SET records_count = records_count - 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.novel_id;
END$$
DELIMITER ;
```

#### 字数统计触发器
```sql
DELIMITER $$
CREATE TRIGGER tr_update_word_count
BEFORE INSERT ON analysis_records
FOR EACH ROW
BEGIN
    SET NEW.word_count = CHAR_LENGTH(IFNULL(NEW.original_text, '')) + CHAR_LENGTH(IFNULL(NEW.comment_text, ''));
END$$

CREATE TRIGGER tr_update_word_count_update
BEFORE UPDATE ON analysis_records
FOR EACH ROW
BEGIN
    SET NEW.word_count = CHAR_LENGTH(IFNULL(NEW.original_text, '')) + CHAR_LENGTH(IFNULL(NEW.comment_text, ''));
END$$
DELIMITER ;
```

## 性能优化建议

### 查询优化
1. **分页查询**：使用LIMIT和OFFSET，考虑游标分页
2. **索引覆盖**：查询字段尽量被索引覆盖
3. **避免全表扫描**：WHERE条件使用索引字段
4. **批量操作**：使用批量INSERT/UPDATE减少IO

### 存储优化
1. **分区表**：按时间分区analysis_records表
2. **归档策略**：定期归档历史数据
3. **压缩**：使用InnoDB压缩减少存储空间

### 缓存策略
1. **查询缓存**：缓存热点查询结果
2. **应用缓存**：缓存分类列表、统计数据
3. **CDN缓存**：缓存封面图片等静态资源

---

*此数据库设计文档将根据实际使用情况进行优化调整。*
