# 前端技术规范

## 技术栈选择

### 核心框架
- **Vue.js 3.4+**: 使用Composition API，支持TypeScript
- **TypeScript 5.0+**: 提供类型安全和更好的开发体验
- **Vite 5.0+**: 快速的构建工具和开发服务器

### UI组件库
- **Element Plus 2.4+**: 成熟的Vue 3组件库
- **@element-plus/icons-vue**: 图标库
- **自定义组件**: 针对特殊需求开发专用组件

### 状态管理
- **Pinia 2.1+**: Vue 3官方推荐的状态管理库
- **持久化**: pinia-plugin-persistedstate

### 路由管理
- **Vue Router 4.2+**: 官方路由库
- **路由守卫**: 实现权限控制和页面访问控制

### HTTP客户端
- **Axios 1.6+**: HTTP请求库
- **请求拦截器**: 统一处理请求头、错误处理
- **响应拦截器**: 统一处理响应格式、错误提示

## 项目结构

```
src/
├── api/                    # API接口定义
│   ├── categories.ts       # 分类相关API
│   ├── novels.ts          # 小说相关API
│   ├── records.ts         # 分析记录相关API
│   └── index.ts           # API统一导出
├── assets/                # 静态资源
│   ├── images/            # 图片资源
│   ├── icons/             # 图标资源
│   └── styles/            # 全局样式
├── components/            # 公共组件
│   ├── common/            # 通用组件
│   ├── forms/             # 表单组件
│   ├── layout/            # 布局组件
│   └── markdown/          # Markdown相关组件
├── composables/           # 组合式函数
│   ├── useApi.ts          # API调用封装
│   ├── useDialog.ts       # 对话框管理
│   └── useSearch.ts       # 搜索功能
├── pages/                 # 页面组件
│   ├── categories/        # 分类管理页面
│   ├── novels/            # 小说管理页面
│   ├── records/           # 分析记录页面
│   └── dashboard/         # 仪表板页面
├── router/                # 路由配置
│   └── index.ts
├── stores/                # 状态管理
│   ├── categories.ts      # 分类状态
│   ├── novels.ts          # 小说状态
│   ├── records.ts         # 记录状态
│   └── user.ts            # 用户状态
├── types/                 # TypeScript类型定义
│   ├── api.ts             # API类型
│   ├── models.ts          # 数据模型类型
│   └── components.ts      # 组件类型
├── utils/                 # 工具函数
│   ├── request.ts         # HTTP请求封装
│   ├── storage.ts         # 本地存储封装
│   ├── format.ts          # 格式化工具
│   └── validation.ts      # 验证工具
├── App.vue               # 根组件
└── main.ts               # 应用入口
```

## 组件设计规范

### 组件命名
- **PascalCase**: 组件文件名和组件名使用大驼峰
- **语义化**: 组件名要能清楚表达功能
- **前缀**: 业务组件使用统一前缀，如`Novel`、`Record`

### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/components'

// 定义Props
interface Props {
  title: string
  data?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

// 定义Emits
interface Emits {
  update: [value: any]
  delete: [id: number]
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const list = ref<any[]>([])

// 计算属性
const filteredList = computed(() => {
  return list.value.filter(item => item.visible)
})

// 方法
const handleUpdate = (value: any) => {
  emit('update', value)
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped lang="scss">
// 样式定义
</style>
```

### 核心组件设计

#### 1. Markdown编辑器组件
```vue
<!-- MarkdownEditor.vue -->
<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button @click="insertBold">粗体</el-button>
        <el-button @click="insertItalic">斜体</el-button>
        <el-button @click="insertCode">代码</el-button>
      </el-button-group>
    </div>
    <div class="editor-content">
      <el-input
        v-model="content"
        type="textarea"
        :rows="rows"
        placeholder="请输入内容..."
        @input="handleInput"
      />
      <div v-if="showPreview" class="preview" v-html="renderedContent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { marked } from 'marked'

interface Props {
  modelValue: string
  rows?: number
  showPreview?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  rows: 10,
  showPreview: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const content = ref(props.modelValue)

const renderedContent = computed(() => {
  return marked(content.value)
})

const handleInput = () => {
  emit('update:modelValue', content.value)
}

watch(() => props.modelValue, (newValue) => {
  content.value = newValue
})
</script>
```

#### 2. 搜索组件
```vue
<!-- SearchBox.vue -->
<template>
  <div class="search-box">
    <el-input
      v-model="searchQuery"
      placeholder="搜索小说、作者或内容..."
      clearable
      @input="handleSearch"
      @clear="handleClear"
    >
      <template #prefix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>
    <div v-if="showSuggestions && suggestions.length" class="suggestions">
      <div
        v-for="item in suggestions"
        :key="item.id"
        class="suggestion-item"
        @click="selectSuggestion(item)"
      >
        {{ item.title }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

interface SearchItem {
  id: number
  title: string
  type: 'novel' | 'record'
}

interface Props {
  placeholder?: string
  showSuggestions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索...',
  showSuggestions: true
})

const emit = defineEmits<{
  search: [query: string]
  select: [item: SearchItem]
}>()

const searchQuery = ref('')
const suggestions = ref<SearchItem[]>([])

const debouncedSearch = debounce((query: string) => {
  emit('search', query)
}, 300)

const handleSearch = () => {
  debouncedSearch(searchQuery.value)
}
</script>
```

## 状态管理规范

### Store结构
```typescript
// stores/novels.ts
import { defineStore } from 'pinia'
import type { Novel, NovelQuery } from '@/types/models'
import { novelsApi } from '@/api/novels'

export const useNovelsStore = defineStore('novels', () => {
  // 状态
  const novels = ref<Novel[]>([])
  const currentNovel = ref<Novel | null>(null)
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    size: 20,
    total: 0
  })

  // Getters
  const novelsByCategory = computed(() => (categoryId: number) => {
    return novels.value.filter(novel => novel.categoryId === categoryId)
  })

  // Actions
  const fetchNovels = async (query?: NovelQuery) => {
    loading.value = true
    try {
      const response = await novelsApi.getList(query)
      novels.value = response.data.novels
      pagination.value = response.data.pagination
    } catch (error) {
      console.error('获取小说列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const createNovel = async (novelData: Partial<Novel>) => {
    try {
      const response = await novelsApi.create(novelData)
      novels.value.unshift(response.data)
      return response.data
    } catch (error) {
      console.error('创建小说失败:', error)
      throw error
    }
  }

  const updateNovel = async (id: number, novelData: Partial<Novel>) => {
    try {
      const response = await novelsApi.update(id, novelData)
      const index = novels.value.findIndex(novel => novel.id === id)
      if (index !== -1) {
        novels.value[index] = response.data
      }
      return response.data
    } catch (error) {
      console.error('更新小说失败:', error)
      throw error
    }
  }

  const deleteNovel = async (id: number) => {
    try {
      await novelsApi.delete(id)
      const index = novels.value.findIndex(novel => novel.id === id)
      if (index !== -1) {
        novels.value.splice(index, 1)
      }
    } catch (error) {
      console.error('删除小说失败:', error)
      throw error
    }
  }

  return {
    // 状态
    novels,
    currentNovel,
    loading,
    pagination,
    // Getters
    novelsByCategory,
    // Actions
    fetchNovels,
    createNovel,
    updateNovel,
    deleteNovel
  }
})
```

## 样式规范

### SCSS变量定义
```scss
// assets/styles/variables.scss
// 颜色系统
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 边框颜色
$border-base: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;
$border-extra-light: #f2f6fc;

// 背景颜色
$bg-color: #ffffff;
$bg-color-page: #f2f3f5;
$bg-color-overlay: rgba(255, 255, 255, 0.9);

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;

// 圆角
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-lg: 8px;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
```

### 组件样式规范
```scss
// 使用BEM命名规范
.novel-card {
  padding: $spacing-md;
  border: 1px solid $border-light;
  border-radius: $border-radius-base;
  background: $bg-color;
  box-shadow: $box-shadow-base;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: $box-shadow-light;
    transform: translateY(-2px);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-sm;
  }

  &__title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-primary;
    margin: 0;
  }

  &__author {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__content {
    margin: $spacing-sm 0;
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-md;
    padding-top: $spacing-sm;
    border-top: 1px solid $border-extra-light;
  }

  &--loading {
    opacity: 0.6;
    pointer-events: none;
  }
}
```

## 工具函数规范

### API请求封装
```typescript
// utils/request.ts
import axios from 'axios'
import type { AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    if (data.success) {
      return data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message))
    }
  },
  (error: AxiosError) => {
    const message = error.response?.data?.message || error.message || '网络错误'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default request
```

### 格式化工具
```typescript
// utils/format.ts
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 格式化日期
export const formatDate = (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(date).format(format)
}

// 相对时间
export const formatRelativeTime = (date: string | Date) => {
  return dayjs(date).fromNow()
}

// 格式化文件大小
export const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化数字
export const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN')
}

// 截断文本
export const truncateText = (text: string, length = 100) => {
  if (text.length <= length) return text
  return text.substring(0, length) + '...'
}
```

## 性能优化

### 组件懒加载
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Dashboard',
      component: () => import('@/pages/dashboard/Index.vue')
    },
    {
      path: '/novels',
      name: 'Novels',
      component: () => import('@/pages/novels/Index.vue')
    }
  ]
})
```

### 虚拟滚动
```vue
<!-- 大列表优化 -->
<template>
  <el-virtual-list
    :data="records"
    :height="400"
    :item-size="80"
  >
    <template #default="{ item }">
      <RecordItem :record="item" />
    </template>
  </el-virtual-list>
</template>
```

### 图片懒加载
```vue
<template>
  <img
    v-lazy="novel.coverImage"
    :alt="novel.title"
    class="novel-cover"
  />
</template>
```

---

*此前端技术规范将随着项目发展持续完善和更新。*
