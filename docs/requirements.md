# 小说分析网站需求文档

## 项目概述

### 项目名称
HaoWriter - 小说分析与拆解管理平台

### 项目目标
构建一个专业的小说分析平台，帮助用户系统化地记录、管理和分析小说内容，提供结构化的分析拆解工具，支持深度阅读和创作学习。

### 目标用户
- 小说爱好者和评论家
- 文学研究者和学生
- 创作者和编剧
- 阅读俱乐部成员

## 功能需求详细说明

### 1. 分类管理系统

#### 1.1 核心功能
- **创建分类**：用户可以创建自定义分类（如：玄幻、都市、历史、科幻等）
- **编辑分类**：修改分类名称、描述、排序等属性
- **删除分类**：支持安全删除（需确认是否包含小说）
- **分类排序**：支持拖拽排序或手动设置优先级

#### 1.2 分类属性
- 分类名称（必填，唯一）
- 分类描述（可选）
- 创建时间
- 更新时间
- 包含小说数量（自动统计）
- 排序权重

#### 1.3 业务规则
- 分类名称不能重复
- 删除分类前需确认处理其下的小说
- 支持批量操作（批量删除、移动等）

### 2. 小说管理系统

#### 2.1 核心功能
- **添加小说**：在指定分类下创建小说条目
- **编辑小说**：修改小说基本信息
- **删除小说**：支持安全删除（需确认是否包含分析记录）
- **小说搜索**：支持按标题、作者、标签搜索
- **小说筛选**：按分类、状态、创建时间等筛选

#### 2.2 小说属性
- 标题（必填）
- 作者（必填）
- 简介（可选，支持Markdown）
- 封面图片（可选）
- 标签（多标签支持）
- 阅读状态（未开始、阅读中、已完成、暂停）
- 个人评分（1-5星）
- 创建时间
- 更新时间
- 分析记录数量（自动统计）

#### 2.3 业务规则
- 同一分类下小说标题不能重复
- 支持小说在分类间移动
- 删除小说时需处理其下的分析记录

### 3. 分析记录系统

#### 3.1 核心功能
- **创建记录**：为指定小说添加分析记录
- **编辑记录**：修改原文和评论内容
- **删除记录**：支持单条或批量删除
- **记录排序**：支持按创建时间、章节顺序等排序
- **记录搜索**：在原文和评论中搜索关键词
- **记录导出**：支持导出为Markdown、PDF等格式

#### 3.2 记录结构
- **原文部分**
  - 章节信息（可选）
  - 原文内容（支持Markdown格式）
  - 页码或位置信息（可选）
  
- **评论部分**
  - 分析类型（情节分析、人物分析、写作技巧、语言特色等）
  - 评论内容（支持Markdown格式）
  - 重要程度标记（1-5星）
  - 标签（多标签支持）

#### 3.3 记录属性
- 记录标题（可选）
- 创建时间
- 更新时间
- 字数统计（自动计算）
- 阅读次数（可选功能）

#### 3.4 业务规则
- 原文部分和评论部分至少有一个不能为空
- 支持记录间的关联和引用
- 支持记录的版本历史（可选功能）

## 数据库设计建议

### 数据表结构

#### categories 表（分类表）
```sql
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order)
);
```

#### novels 表（小说表）
```sql
CREATE TABLE novels (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT NOT NULL COMMENT '分类ID',
    title VARCHAR(200) NOT NULL COMMENT '小说标题',
    author VARCHAR(100) NOT NULL COMMENT '作者',
    description TEXT COMMENT '简介',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    tags JSON COMMENT '标签数组',
    reading_status ENUM('not_started', 'reading', 'completed', 'paused') DEFAULT 'not_started',
    personal_rating TINYINT COMMENT '个人评分(1-5)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_title (title),
    INDEX idx_author (author)
);
```

#### analysis_records 表（分析记录表）
```sql
CREATE TABLE analysis_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    novel_id BIGINT NOT NULL COMMENT '小说ID',
    title VARCHAR(200) COMMENT '记录标题',
    chapter_info VARCHAR(100) COMMENT '章节信息',
    original_text LONGTEXT COMMENT '原文内容',
    comment_text LONGTEXT COMMENT '评论内容',
    analysis_type VARCHAR(50) COMMENT '分析类型',
    importance_level TINYINT DEFAULT 3 COMMENT '重要程度(1-5)',
    tags JSON COMMENT '标签数组',
    page_location VARCHAR(100) COMMENT '页码或位置',
    word_count INT DEFAULT 0 COMMENT '字数统计',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    INDEX idx_novel_id (novel_id),
    INDEX idx_analysis_type (analysis_type),
    INDEX idx_importance_level (importance_level),
    FULLTEXT idx_content (original_text, comment_text)
);
```

### 数据关系
- categories : novels = 1 : N
- novels : analysis_records = 1 : N
- 支持软删除机制（可选）
- 建议使用数据库事务确保数据一致性

## 用户界面设计要求

### 整体设计原则
- **简洁明了**：界面布局清晰，功能入口明显
- **响应式设计**：适配桌面、平板、手机等多种设备
- **用户友好**：操作流程简单，学习成本低
- **视觉舒适**：支持明暗主题切换，护眼模式

### 主要页面设计

#### 1. 首页/仪表板
- 显示分类概览和统计信息
- 最近添加的小说和分析记录
- 快速操作入口（添加分类、小说、记录）
- 搜索框（全局搜索）

#### 2. 分类管理页面
- 分类列表展示（卡片或表格形式）
- 分类操作按钮（编辑、删除、排序）
- 添加分类的浮动按钮或固定入口
- 分类下小说数量显示

#### 3. 小说列表页面
- 小说卡片展示（包含封面、标题、作者、评分）
- 筛选和排序功能
- 搜索功能
- 批量操作支持

#### 4. 小说详情页面
- 小说基本信息展示
- 分析记录列表
- 添加记录的快速入口
- 统计信息（总记录数、总字数等）

#### 5. 分析记录编辑页面
- 双栏布局：左侧原文编辑，右侧评论编辑
- Markdown编辑器和实时预览
- 工具栏（格式化、插入链接、图片等）
- 自动保存功能

### UI组件要求
- **Markdown编辑器**：支持语法高亮、实时预览、工具栏
- **富文本渲染**：支持代码高亮、表格、链接等
- **标签组件**：支持多标签选择和自定义标签
- **评分组件**：星级评分显示和编辑
- **搜索组件**：支持关键词高亮和筛选
- **确认对话框**：删除等危险操作的二次确认

### 交互设计
- 支持键盘快捷键（保存、搜索等）
- 拖拽排序支持
- 右键菜单（复制、删除等）
- 面包屑导航
- 加载状态和错误提示

## 技术架构建议

### 前端技术栈
- **框架**：Vue.js 3 + TypeScript
- **UI库**：Element Plus 或 Ant Design Vue
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **Markdown编辑器**：@vueup/vue-quill 或 v-md-editor
- **HTTP客户端**：Axios
- **构建工具**：Vite
- **CSS预处理器**：SCSS

### 后端技术栈
- **框架**：Node.js + Express 或 Nest.js
- **数据库**：MySQL 8.0+ 或 PostgreSQL
- **ORM**：TypeORM 或 Prisma
- **身份认证**：JWT
- **文件存储**：本地存储 或 云存储（阿里云OSS、腾讯云COS）
- **API文档**：Swagger/OpenAPI

### 部署架构
- **容器化**：Docker + Docker Compose
- **反向代理**：Nginx
- **数据库**：MySQL/PostgreSQL 容器
- **文件存储**：挂载卷或对象存储
- **监控**：日志收集和性能监控

### 开发工具
- **代码规范**：ESLint + Prettier
- **Git钩子**：Husky + lint-staged
- **API测试**：Postman 或 Insomnia
- **版本控制**：Git + GitHub/GitLab

## 开发优先级和里程碑规划

### 第一阶段：核心功能开发（4-6周）

#### 里程碑1：基础架构搭建（1周）
- [ ] 项目初始化和环境配置
- [ ] 数据库设计和创建
- [ ] 基础API框架搭建
- [ ] 前端项目初始化

#### 里程碑2：分类和小说管理（2周）
- [ ] 分类CRUD功能
- [ ] 小说CRUD功能
- [ ] 基础UI界面
- [ ] 数据验证和错误处理

#### 里程碑3：分析记录核心功能（2-3周）
- [ ] 分析记录CRUD功能
- [ ] Markdown编辑器集成
- [ ] 基础搜索功能
- [ ] 数据关联和统计

### 第二阶段：功能完善（3-4周）

#### 里程碑4：用户体验优化（2周）
- [ ] 响应式设计完善
- [ ] 交互优化（拖拽、快捷键等）
- [ ] 加载状态和错误提示
- [ ] 数据导出功能

#### 里程碑5：高级功能（1-2周）
- [ ] 高级搜索和筛选
- [ ] 标签系统完善
- [ ] 批量操作功能
- [ ] 主题切换功能

### 第三阶段：测试和部署（2-3周）

#### 里程碑6：测试和优化（1-2周）
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能优化
- [ ] 安全性检查

#### 里程碑7：部署上线（1周）
- [ ] 生产环境配置
- [ ] 部署脚本编写
- [ ] 监控和日志配置
- [ ] 用户文档编写

### 可选功能（后续版本）
- 用户系统和权限管理
- 数据备份和恢复
- 移动端APP
- 协作功能（多用户共享）
- AI辅助分析功能
- 数据可视化图表

## 风险评估和应对策略

### 技术风险
- **Markdown编辑器性能**：选择成熟的编辑器组件，进行性能测试
- **数据库性能**：合理设计索引，考虑分页和缓存策略
- **文件存储**：制定备份策略，考虑云存储方案

### 业务风险
- **需求变更**：采用敏捷开发，保持架构灵活性
- **用户体验**：早期进行用户测试，收集反馈
- **数据安全**：实施数据备份和访问控制

### 时间风险
- **开发延期**：合理评估工作量，预留缓冲时间
- **技术难点**：提前进行技术调研和原型验证

---

*本需求文档将根据开发过程中的反馈和需求变化进行持续更新和完善。*
